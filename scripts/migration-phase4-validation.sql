-- =====================================================
-- PHASE 4: COMPREHENSIVE VALIDATION
-- Validate data integrity after migration
-- =====================================================

-- Comprehensive validation report
SELECT 'MIGRATION VALIDATION REPORT' as report_section;
SELECT '================================' as separator;

-- 1. Data Completeness Validation
SELECT 'DATA COMPLETENESS' as validation_category;

-- Check all managers have employee records
WITH manager_employee_check AS (
    SELECT 
        m.user_id,
        m.email as manager_email,
        m.full_name as manager_name,
        e.id as employee_id,
        e.email as employee_email,
        e.full_name as employee_name
    FROM appy_managers m
    LEFT JOIN appy_employees e ON m.email = e.email
)
SELECT 
    'Managers without employee records' as check_name,
    COUNT(*) as count,
    CASE WHEN COUNT(*) = 0 THEN '✅ PASS' ELSE '❌ FAIL' END as status
FROM manager_employee_check
WHERE employee_id IS NULL;

-- Check all migrated employees have system_role
SELECT 
    'Migrated employees missing system_role' as check_name,
    COUNT(*) as count,
    CASE WHEN COUNT(*) = 0 THEN '✅ PASS' ELSE '❌ FAIL' END as status
FROM appy_employees e
INNER JOIN appy_managers m ON e.email = m.email
WHERE e.system_role IS NULL;

-- 2. Data Consistency Validation
SELECT 'DATA CONSISTENCY' as validation_category;

-- Check clerk_user_id consistency
SELECT 
    'Clerk ID mismatches' as check_name,
    COUNT(*) as count,
    CASE WHEN COUNT(*) = 0 THEN '✅ PASS' ELSE '❌ FAIL' END as status
FROM appy_employees e
INNER JOIN appy_managers m ON e.email = m.email
WHERE e.clerk_user_id != m.user_id;

-- Check role consistency
SELECT 
    'Role rank inconsistencies' as check_name,
    COUNT(*) as count,
    CASE WHEN COUNT(*) = 0 THEN '✅ PASS' ELSE '⚠️ REVIEW' END as status
FROM appy_employees e
INNER JOIN appy_managers m ON e.email = m.email
WHERE (m.role = 'super-admin' AND e.role_rank != 'super-admin')
   OR (m.role = 'senior-manager' AND e.role_rank != 'senior-manager')
   OR (m.role = 'manager' AND e.role_rank NOT IN ('manager', 'senior-manager'));

-- 3. Foreign Key Integrity Validation
SELECT 'FOREIGN KEY INTEGRITY' as validation_category;

-- Check appraisals table references
SELECT 
    'Appraisals with invalid manager_id' as check_name,
    COUNT(*) as count,
    CASE WHEN COUNT(*) = 0 THEN '✅ PASS' ELSE '❌ FAIL' END as status
FROM appy_appraisals a
LEFT JOIN appy_employees e ON a.manager_id = e.clerk_user_id
WHERE e.clerk_user_id IS NULL;

-- Check employee_managers table references
SELECT 
    'Employee-manager relationships with invalid manager_id' as check_name,
    COUNT(*) as count,
    CASE WHEN COUNT(*) = 0 THEN '✅ PASS' ELSE '❌ FAIL' END as status
FROM appy_employee_managers em
LEFT JOIN appy_employees e ON em.manager_id = e.clerk_user_id
WHERE e.clerk_user_id IS NULL;

-- 4. Business Logic Validation
SELECT 'BUSINESS LOGIC' as validation_category;

-- Check that super-admins have appropriate role_rank
SELECT 
    'Super-admins with incorrect role_rank' as check_name,
    COUNT(*) as count,
    CASE WHEN COUNT(*) = 0 THEN '✅ PASS' ELSE '⚠️ REVIEW' END as status
FROM appy_employees
WHERE system_role = 'super-admin' AND role_rank != 'super-admin';

-- Check that managers have manager-level role_rank
SELECT 
    'Managers with employee role_rank' as check_name,
    COUNT(*) as count,
    CASE WHEN COUNT(*) = 0 THEN '✅ PASS' ELSE '⚠️ REVIEW' END as status
FROM appy_employees
WHERE system_role = 'manager' AND role_rank = 'employee';

-- 5. Data Quality Report
SELECT 'DATA QUALITY SUMMARY' as validation_category;

SELECT 
    'Total employees after migration' as metric,
    COUNT(*) as value
FROM appy_employees
UNION ALL
SELECT 
    'Employees with system_role' as metric,
    COUNT(*) as value
FROM appy_employees
WHERE system_role IS NOT NULL
UNION ALL
SELECT 
    'Employees with clerk_user_id' as metric,
    COUNT(*) as value
FROM appy_employees
WHERE clerk_user_id IS NOT NULL
UNION ALL
SELECT 
    'Employees with additional_roles' as metric,
    COUNT(*) as value
FROM appy_employees
WHERE jsonb_array_length(additional_roles) > 0;

-- 6. Detailed Issue Report (if any)
SELECT 'DETAILED ISSUES' as validation_category;

-- Show any remaining issues
SELECT 
    'CLERK_ID_MISMATCH' as issue_type,
    e.email,
    e.full_name,
    e.clerk_user_id as employee_clerk_id,
    m.user_id as manager_clerk_id
FROM appy_employees e
INNER JOIN appy_managers m ON e.email = m.email
WHERE e.clerk_user_id != m.user_id

UNION ALL

SELECT 
    'MISSING_SYSTEM_ROLE' as issue_type,
    e.email,
    e.full_name,
    'NULL' as employee_clerk_id,
    m.user_id as manager_clerk_id
FROM appy_employees e
INNER JOIN appy_managers m ON e.email = m.email
WHERE e.system_role IS NULL

UNION ALL

SELECT 
    'ROLE_RANK_MISMATCH' as issue_type,
    e.email,
    e.full_name,
    e.role_rank::text as employee_role_rank,
    m.role as manager_role
FROM appy_employees e
INNER JOIN appy_managers m ON e.email = m.email
WHERE (m.role = 'super-admin' AND e.role_rank != 'super-admin')
   OR (m.role = 'senior-manager' AND e.role_rank != 'senior-manager')
   OR (m.role = 'manager' AND e.role_rank NOT IN ('manager', 'senior-manager'))
ORDER BY issue_type, email;

-- 7. Migration Success Summary
SELECT 'MIGRATION SUCCESS SUMMARY' as validation_category;

WITH validation_summary AS (
    SELECT 
        (SELECT COUNT(*) FROM appy_managers) as total_managers,
        (SELECT COUNT(*) FROM appy_employees WHERE system_role IS NOT NULL) as migrated_employees,
        (SELECT COUNT(*) FROM appy_employees e INNER JOIN appy_managers m ON e.email = m.email WHERE e.clerk_user_id != m.user_id) as clerk_id_issues,
        (SELECT COUNT(*) FROM appy_employees e INNER JOIN appy_managers m ON e.email = m.email WHERE e.system_role IS NULL) as missing_roles
)
SELECT 
    CASE 
        WHEN migrated_employees = total_managers AND clerk_id_issues = 0 AND missing_roles = 0 
        THEN '🎉 MIGRATION SUCCESSFUL - All data migrated correctly'
        WHEN migrated_employees = total_managers AND (clerk_id_issues > 0 OR missing_roles > 0)
        THEN '⚠️ MIGRATION MOSTLY SUCCESSFUL - Minor issues need review'
        ELSE '❌ MIGRATION INCOMPLETE - Major issues need resolution'
    END as migration_status,
    total_managers,
    migrated_employees,
    clerk_id_issues,
    missing_roles
FROM validation_summary;

RAISE NOTICE 'Phase 4 Validation Complete - Review all validation results above';
