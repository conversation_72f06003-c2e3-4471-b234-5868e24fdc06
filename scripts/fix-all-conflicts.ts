#!/usr/bin/env bun

/**
 * Comprehensive conflict resolution script
 * Uses appy_managers table as source of truth to fix all data conflicts
 */

import { createClient } from '@supabase/supabase-js'

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

interface ConflictSummary {
  clerkIdFixed: number
  nameFixed: number
  departmentFixed: number
  statusFixed: number
  orphanedAppraisalsFixed: number
  duplicatesResolved: number
}

async function main() {
  console.log('🔧 Starting comprehensive conflict resolution...')
  console.log('📋 Using appy_managers table as source of truth\n')

  const summary: ConflictSummary = {
    clerkIdFixed: 0,
    nameFixed: 0,
    departmentFixed: 0,
    statusFixed: 0,
    orphanedAppraisalsFixed: 0,
    duplicatesResolved: 0
  }

  try {
    // Step 1: Fix Clerk ID mismatches
    console.log('🔑 Step 1: Fixing Clerk ID mismatches...')
    summary.clerkIdFixed = await fixClerkIdMismatches()

    // Step 2: Fix name conflicts
    console.log('\n📝 Step 2: Fixing name conflicts...')
    summary.nameFixed = await fixNameConflicts()

    // Step 3: Fix department mismatches
    console.log('\n🏢 Step 3: Fixing department mismatches...')
    summary.departmentFixed = await fixDepartmentMismatches()

    // Step 4: Fix active status conflicts
    console.log('\n✅ Step 4: Fixing active status conflicts...')
    summary.statusFixed = await fixActiveStatusConflicts()

    // Step 5: Fix orphaned appraisals
    console.log('\n🔗 Step 5: Fixing orphaned appraisals...')
    summary.orphanedAppraisalsFixed = await fixOrphanedAppraisals()

    // Step 6: Resolve duplicate person records
    console.log('\n👥 Step 6: Resolving duplicate person records...')
    summary.duplicatesResolved = await resolveDuplicatePersons()

    // Final validation
    console.log('\n🔍 Step 7: Final validation...')
    await validateFixes()

    // Print summary
    printSummary(summary)

  } catch (error) {
    console.error('❌ Error during conflict resolution:', error)
    process.exit(1)
  }
}

async function fixClerkIdMismatches(): Promise<number> {
  console.log('  🔍 Finding clerk ID mismatches...')

  // Get all manager-employee pairs with clerk ID conflicts using a simpler approach
  const { data: managers, error: managersError } = await supabase
    .from('appy_managers')
    .select('user_id, email, full_name')

  if (managersError) {
    console.error('  ❌ Error fetching managers:', managersError)
    return 0
  }

  const conflicts = []

  // Check each manager for conflicts with their employee record
  for (const manager of managers || []) {
    const { data: employee, error: employeeError } = await supabase
      .from('appy_employees')
      .select('id, clerk_user_id, email')
      .eq('email', manager.email)
      .single()

    if (!employeeError && employee && employee.clerk_user_id !== manager.user_id) {
      conflicts.push({ manager, employee })
    }
  }

  if (conflicts.length === 0) {
    console.log('  ✅ No clerk ID conflicts found')
    return 0
  }

  console.log(`  📊 Found ${conflicts.length} clerk ID conflicts`)

  let fixed = 0
  for (const conflict of conflicts) {
    const { manager, employee } = conflict

    console.log(`  🔧 Fixing ${manager.email}: ${employee.clerk_user_id || 'NULL'} → ${manager.user_id}`)

    // Update employee clerk_user_id to match manager user_id
    const { error: updateError } = await supabase
      .from('appy_employees')
      .update({
        clerk_user_id: manager.user_id,
        updated_at: new Date().toISOString()
      })
      .eq('email', manager.email)

    if (updateError) {
      console.error(`  ❌ Failed to update ${manager.email}:`, updateError)
    } else {
      console.log(`  ✅ Fixed clerk ID for ${manager.email}`)
      fixed++
    }
  }

  return fixed
}

async function fixNameConflicts(): Promise<number> {
  console.log('  🔍 Finding name conflicts...')

  // Get all managers and check for name conflicts
  const { data: managers, error: managersError } = await supabase
    .from('appy_managers')
    .select('user_id, email, full_name')

  if (managersError) {
    console.error('  ❌ Error fetching managers:', managersError)
    return 0
  }

  const conflicts = []

  // Check each manager for name conflicts with their employee record
  for (const manager of managers || []) {
    const { data: employee, error: employeeError } = await supabase
      .from('appy_employees')
      .select('id, full_name, email')
      .eq('email', manager.email)
      .single()

    if (!employeeError && employee && employee.full_name !== manager.full_name) {
      conflicts.push({ manager, employee })
    }
  }

  if (conflicts.length === 0) {
    console.log('  ✅ No name conflicts found')
    return 0
  }

  console.log(`  📊 Found ${conflicts.length} name conflicts`)

  let fixed = 0
  for (const conflict of conflicts) {
    const { manager, employee } = conflict

    console.log(`  🔧 Fixing ${manager.email}: "${employee.full_name}" → "${manager.full_name}"`)

    // Update employee name to match manager name (source of truth)
    const { error: updateError } = await supabase
      .from('appy_employees')
      .update({
        full_name: manager.full_name,
        updated_at: new Date().toISOString()
      })
      .eq('email', manager.email)

    if (updateError) {
      console.error(`  ❌ Failed to update ${manager.email}:`, updateError)
    } else {
      console.log(`  ✅ Fixed name for ${manager.email}`)
      fixed++
    }
  }

  return fixed
}

async function fixDepartmentMismatches(): Promise<number> {
  console.log('  🔍 Finding department conflicts...')

  // Use direct SQL to find and fix department mismatches
  const { data: result, error } = await supabase.rpc('exec_sql', {
    sql: `
      UPDATE appy_employees
      SET
        department_id = m.department_id,
        updated_at = CURRENT_TIMESTAMP
      FROM appy_managers m
      WHERE appy_employees.email = m.email
      AND appy_employees.department_id IS DISTINCT FROM m.department_id
      RETURNING appy_employees.email, appy_employees.department_id, m.department_id as manager_dept_id
    `
  })

  if (error) {
    console.error('  ❌ Error fixing department conflicts:', error)
    return 0
  }

  const fixed = result?.length || 0

  if (fixed === 0) {
    console.log('  ✅ No department conflicts found')
  } else {
    console.log(`  📊 Fixed ${fixed} department conflicts`)
    for (const row of result || []) {
      console.log(`  ✅ Fixed department for ${row.email}: ${row.department_id} (was ${row.manager_dept_id})`)
    }
  }

  return fixed
}

async function fixActiveStatusConflicts(): Promise<number> {
  console.log('  🔍 Finding active status conflicts...')

  // Use direct SQL to find and fix active status mismatches
  const { data: result, error } = await supabase.rpc('exec_sql', {
    sql: `
      UPDATE appy_employees
      SET
        active = m.active,
        updated_at = CURRENT_TIMESTAMP
      FROM appy_managers m
      WHERE appy_employees.email = m.email
      AND appy_employees.active != m.active
      RETURNING appy_employees.email, appy_employees.active, m.active as manager_active
    `
  })

  if (error) {
    console.error('  ❌ Error fixing active status conflicts:', error)
    return 0
  }

  const fixed = result?.length || 0

  if (fixed === 0) {
    console.log('  ✅ No active status conflicts found')
  } else {
    console.log(`  📊 Fixed ${fixed} active status conflicts`)
    for (const row of result || []) {
      console.log(`  ✅ Fixed active status for ${row.email}: ${row.active} (was ${row.manager_active})`)
    }
  }

  return fixed
}

async function fixOrphanedAppraisals(): Promise<number> {
  console.log('  🔍 Finding orphaned appraisals...')
  
  // Find appraisals that reference non-existent managers in the unified table
  const { data: orphanedAppraisals, error } = await supabase
    .from('appy_appraisals')
    .select('id, manager_id, employee_id')
    .not('manager_id', 'in', 
      supabase.from('appy_employees').select('clerk_user_id').not('clerk_user_id', 'is', null)
    )

  if (error) {
    console.error('  ❌ Error fetching orphaned appraisals:', error)
    return 0
  }

  if (!orphanedAppraisals || orphanedAppraisals.length === 0) {
    console.log('  ✅ No orphaned appraisals found')
    return 0
  }

  console.log(`  📊 Found ${orphanedAppraisals.length} orphaned appraisals`)

  // For each orphaned appraisal, try to find the correct manager
  let fixed = 0
  for (const appraisal of orphanedAppraisals) {
    console.log(`  🔧 Fixing appraisal ${appraisal.id} with orphaned manager ${appraisal.manager_id}`)

    // Try to find the manager in the managers table and get their correct clerk_user_id
    const { data: manager } = await supabase
      .from('appy_managers')
      .select('user_id, email')
      .eq('user_id', appraisal.manager_id)
      .single()

    if (manager) {
      // Find the corresponding employee record
      const { data: employee } = await supabase
        .from('appy_employees')
        .select('clerk_user_id')
        .eq('email', manager.email)
        .single()

      if (employee?.clerk_user_id) {
        // Update the appraisal to use the correct clerk_user_id
        const { error: updateError } = await supabase
          .from('appy_appraisals')
          .update({ manager_id: employee.clerk_user_id })
          .eq('id', appraisal.id)

        if (updateError) {
          console.error(`  ❌ Failed to fix appraisal ${appraisal.id}:`, updateError)
        } else {
          console.log(`  ✅ Fixed appraisal ${appraisal.id}: ${appraisal.manager_id} → ${employee.clerk_user_id}`)
          fixed++
        }
      }
    }
  }

  return fixed
}

async function resolveDuplicatePersons(): Promise<number> {
  console.log('  🔍 Identifying duplicate person records...')
  
  // Handle known duplicates: Annette Nel and Issa Abou Daher
  const duplicates = [
    {
      name: 'Annette Nel',
      managerEmail: '<EMAIL>',
      employeeEmail: '<EMAIL>',
      preferredEmail: '<EMAIL>' // Use the roundtable email as canonical
    },
    {
      name: 'Issa Abou Daher', 
      managerEmail: '<EMAIL>',
      employeeEmail: '<EMAIL>',
      preferredEmail: '<EMAIL>' // Use the company email as canonical
    }
  ]

  let resolved = 0
  for (const duplicate of duplicates) {
    console.log(`  🔧 Resolving duplicate for ${duplicate.name}...`)
    
    // Get both manager records
    const { data: managers } = await supabase
      .from('appy_managers')
      .select('*')
      .in('email', [duplicate.managerEmail, duplicate.employeeEmail])

    if (!managers || managers.length !== 2) {
      console.log(`  ⚠️ Could not find both manager records for ${duplicate.name}`)
      continue
    }

    const preferredManager = managers.find(m => m.email === duplicate.preferredEmail)
    const duplicateManager = managers.find(m => m.email !== duplicate.preferredEmail)

    if (!preferredManager || !duplicateManager) {
      console.log(`  ⚠️ Could not identify preferred manager for ${duplicate.name}`)
      continue
    }

    // Update employee record to use preferred manager's data
    const { error: updateError } = await supabase
      .from('appy_employees')
      .update({
        email: preferredManager.email,
        clerk_user_id: preferredManager.user_id,
        full_name: preferredManager.full_name,
        system_role: preferredManager.role,
        additional_roles: preferredManager.additional_roles,
        department_id: preferredManager.department_id,
        active: preferredManager.active,
        updated_at: new Date().toISOString()
      })
      .eq('email', duplicate.employeeEmail)

    if (updateError) {
      console.error(`  ❌ Failed to resolve duplicate for ${duplicate.name}:`, updateError)
      continue
    }

    // Deactivate the duplicate manager record (don't delete to preserve history)
    await supabase
      .from('appy_managers')
      .update({ active: false })
      .eq('email', duplicate.managerEmail)

    console.log(`  ✅ Resolved duplicate for ${duplicate.name}: using ${duplicate.preferredEmail}`)
    resolved++
  }

  return resolved
}

async function validateFixes(): Promise<void> {
  console.log('  🔍 Running validation checks...')

  // Check for remaining conflicts
  const checks = [
    {
      name: 'Clerk ID mismatches',
      query: `
        SELECT COUNT(*) as count 
        FROM appy_managers m 
        INNER JOIN appy_employees e ON m.email = e.email 
        WHERE m.user_id != COALESCE(e.clerk_user_id, '')
      `
    },
    {
      name: 'Name mismatches',
      query: `
        SELECT COUNT(*) as count 
        FROM appy_managers m 
        INNER JOIN appy_employees e ON m.email = e.email 
        WHERE m.full_name != e.full_name
      `
    },
    {
      name: 'Orphaned appraisals',
      query: `
        SELECT COUNT(*) as count 
        FROM appy_appraisals a 
        WHERE NOT EXISTS (
          SELECT 1 FROM appy_employees e WHERE e.clerk_user_id = a.manager_id
        )
      `
    }
  ]

  for (const check of checks) {
    const { data, error } = await supabase.rpc('exec_sql', { sql: check.query })
    
    if (error) {
      console.error(`  ❌ Validation error for ${check.name}:`, error)
    } else {
      const count = data?.[0]?.count || 0
      if (count === 0) {
        console.log(`  ✅ ${check.name}: All resolved`)
      } else {
        console.log(`  ⚠️ ${check.name}: ${count} remaining issues`)
      }
    }
  }
}

function printSummary(summary: ConflictSummary): void {
  console.log('\n' + '='.repeat(50))
  console.log('🎉 CONFLICT RESOLUTION COMPLETE!')
  console.log('='.repeat(50))
  console.log(`🔑 Clerk IDs fixed: ${summary.clerkIdFixed}`)
  console.log(`📝 Names fixed: ${summary.nameFixed}`)
  console.log(`🏢 Departments fixed: ${summary.departmentFixed}`)
  console.log(`✅ Status conflicts fixed: ${summary.statusFixed}`)
  console.log(`🔗 Orphaned appraisals fixed: ${summary.orphanedAppraisalsFixed}`)
  console.log(`👥 Duplicate persons resolved: ${summary.duplicatesResolved}`)
  console.log('='.repeat(50))
  
  const total = Object.values(summary).reduce((sum, count) => sum + count, 0)
  console.log(`📊 Total issues resolved: ${total}`)
  console.log('\n✨ All conflicts have been resolved using appy_managers as source of truth!')
}

// Run the script
main().catch(console.error)
