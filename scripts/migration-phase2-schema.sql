-- =====================================================
-- PHASE 2: SCHEMA EXTENSION
-- Add required columns to appy_employees table
-- =====================================================

-- Add system role columns to appy_employees
ALTER TABLE appy_employees 
ADD COLUMN IF NOT EXISTS system_role appy_user_role DEFAULT NULL,
ADD COLUMN IF NOT EXISTS additional_roles JSONB DEFAULT '[]'::jsonb;

-- Ensure clerk_user_id is properly indexed for performance
CREATE INDEX IF NOT EXISTS idx_appy_employees_clerk_user_id ON appy_employees(clerk_user_id);
CREATE INDEX IF NOT EXISTS idx_appy_employees_system_role ON appy_employees(system_role);
CREATE INDEX IF NOT EXISTS idx_appy_employees_email ON appy_employees(email);

-- Add comments to document the new fields
COMMENT ON COLUMN appy_employees.system_role IS 'System permission role: super-admin, hr-admin, manager, accountant, senior-manager';
COMMENT ON COLUMN appy_employees.additional_roles IS 'JSONB array of additional system roles for multi-role users';

-- Create a function to validate role consistency
CREATE OR REPLACE FUNCTION validate_employee_role_consistency()
RETURNS TRIGGER AS $$
BEGIN
    -- Ensure system_role and role_rank are consistent
    IF NEW.system_role IS NOT NULL THEN
        CASE NEW.system_role
            WHEN 'super-admin' THEN
                IF NEW.role_rank != 'super-admin' THEN
                    NEW.role_rank := 'super-admin';
                END IF;
            WHEN 'senior-manager' THEN
                IF NEW.role_rank NOT IN ('senior-manager', 'super-admin') THEN
                    NEW.role_rank := 'senior-manager';
                END IF;
            WHEN 'manager' THEN
                IF NEW.role_rank NOT IN ('manager', 'senior-manager', 'super-admin') THEN
                    NEW.role_rank := 'manager';
                END IF;
            WHEN 'hr-admin', 'accountant' THEN
                -- These can be any role_rank, no change needed
                NULL;
        END CASE;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for role consistency (optional - can be enabled later)
-- CREATE TRIGGER trigger_validate_employee_role_consistency
--     BEFORE INSERT OR UPDATE ON appy_employees
--     FOR EACH ROW EXECUTE FUNCTION validate_employee_role_consistency();

-- Verify schema changes
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'appy_employees' 
AND column_name IN ('system_role', 'additional_roles')
ORDER BY ordinal_position;

-- Check indexes
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'appy_employees' 
AND indexname LIKE '%system_role%' OR indexname LIKE '%clerk_user_id%' OR indexname LIKE '%email%';

RAISE NOTICE 'Phase 2 Schema Extension Complete - Ready for Phase 3 Data Migration';
