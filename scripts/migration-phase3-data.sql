-- =====================================================
-- PHASE 3: DATA MIGRATION
-- Migrate manager data to employee records
-- =====================================================

-- Create migration log table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.migration_log (
    id SERIAL PRIMARY KEY,
    phase TEXT NOT NULL,
    action TEXT NOT NULL,
    affected_records INTEGER,
    timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    details TEXT,
    success BOOLEAN DEFAULT true
);

-- Step 1: Update existing employees who are also managers
UPDATE appy_employees 
SET 
    system_role = m.role,
    additional_roles = m.additional_roles,
    clerk_user_id = COALESCE(appy_employees.clerk_user_id, m.user_id),
    -- Handle name conflicts by preferring manager name if employee name is null/empty
    full_name = CASE 
        WHEN appy_employees.full_name IS NULL OR appy_employees.full_name = '' THEN m.full_name
        ELSE appy_employees.full_name
    END,
    -- Update role_rank to match system_role
    role_rank = CASE m.role
        WHEN 'super-admin' THEN 'super-admin'::appy_role_rank
        WHEN 'senior-manager' THEN 'senior-manager'::appy_role_rank
        WHEN 'manager' THEN 'manager'::appy_role_rank
        WHEN 'hr-admin' THEN COALESCE(appy_employees.role_rank, 'manager'::appy_role_rank)
        WHEN 'accountant' THEN COALESCE(appy_employees.role_rank, 'employee'::appy_role_rank)
        ELSE appy_employees.role_rank
    END,
    -- Update department if employee doesn't have one but manager does
    department_id = COALESCE(appy_employees.department_id, m.department_id),
    updated_at = CURRENT_TIMESTAMP
FROM appy_managers m 
WHERE appy_employees.email = m.email;

-- Log the update
INSERT INTO public.migration_log (phase, action, affected_records, timestamp, details)
SELECT 
    'phase3' as phase,
    'update_existing_employees' as action,
    COUNT(*) as affected_records,
    CURRENT_TIMESTAMP as timestamp,
    'Updated existing employee records with manager data' as details
FROM appy_employees e
INNER JOIN appy_managers m ON e.email = m.email
WHERE e.system_role IS NOT NULL;

-- Step 2: Create employee records for managers who don't have employee records
INSERT INTO appy_employees (
    full_name, 
    email, 
    clerk_user_id, 
    system_role, 
    additional_roles, 
    role_rank,
    department_id,
    active, 
    rate, 
    created_at,
    updated_at
)
SELECT 
    m.full_name, 
    m.email, 
    m.user_id, 
    m.role,
    m.additional_roles, 
    CASE m.role
        WHEN 'super-admin' THEN 'super-admin'::appy_role_rank
        WHEN 'senior-manager' THEN 'senior-manager'::appy_role_rank
        WHEN 'manager' THEN 'manager'::appy_role_rank
        WHEN 'hr-admin' THEN 'manager'::appy_role_rank  -- HR admins are typically managers
        WHEN 'accountant' THEN 'employee'::appy_role_rank  -- Accountants are typically employees
        ELSE 'employee'::appy_role_rank
    END as role_rank,
    m.department_id,
    m.active, 
    'monthly'::appy_compensation_type as rate,  -- Default to monthly for managers
    m.created_at,
    CURRENT_TIMESTAMP as updated_at
FROM appy_managers m
WHERE NOT EXISTS (
    SELECT 1 FROM appy_employees e WHERE e.email = m.email
);

-- Log the insertion
INSERT INTO public.migration_log (phase, action, affected_records, timestamp, details)
SELECT 
    'phase3' as phase,
    'create_new_employees' as action,
    COUNT(*) as affected_records,
    CURRENT_TIMESTAMP as timestamp,
    'Created new employee records for managers-only users' as details
FROM appy_managers m
WHERE NOT EXISTS (
    SELECT 1 FROM appy_employees e WHERE e.email = m.email
);

-- Step 3: Validation queries
SELECT 'MIGRATION VALIDATION' as validation_type;

-- Check that all managers now have corresponding employee records
SELECT 
    'Managers without employee records' as check_type,
    COUNT(*) as count
FROM appy_managers m
LEFT JOIN appy_employees e ON m.email = e.email
WHERE e.email IS NULL;

-- Check that all migrated employees have system_role set
SELECT 
    'Employees missing system_role' as check_type,
    COUNT(*) as count
FROM appy_employees e
INNER JOIN appy_managers m ON e.email = m.email
WHERE e.system_role IS NULL;

-- Check clerk_user_id consistency
SELECT 
    'Clerk ID mismatches' as check_type,
    COUNT(*) as count
FROM appy_employees e
INNER JOIN appy_managers m ON e.email = m.email
WHERE e.clerk_user_id != m.user_id;

-- Summary of migrated data
SELECT 
    'Total employees after migration' as metric,
    COUNT(*) as count
FROM appy_employees
UNION ALL
SELECT 
    'Employees with system_role' as metric,
    COUNT(*) as count
FROM appy_employees
WHERE system_role IS NOT NULL
UNION ALL
SELECT 
    'Employees with additional_roles' as metric,
    COUNT(*) as count
FROM appy_employees
WHERE jsonb_array_length(additional_roles) > 0;

-- Create a view for easy verification
CREATE OR REPLACE VIEW migration_verification AS
SELECT 
    e.id as employee_id,
    e.email,
    e.full_name as employee_name,
    e.system_role,
    e.additional_roles,
    e.role_rank,
    e.clerk_user_id,
    m.user_id as manager_user_id,
    m.full_name as manager_name,
    m.role as manager_role,
    m.additional_roles as manager_additional_roles,
    CASE 
        WHEN m.user_id IS NULL THEN 'EMPLOYEE_ONLY'
        WHEN e.system_role IS NULL THEN 'MIGRATION_INCOMPLETE'
        WHEN e.clerk_user_id != m.user_id THEN 'CLERK_ID_MISMATCH'
        ELSE 'MIGRATED_OK'
    END as migration_status
FROM appy_employees e
LEFT JOIN appy_managers m ON e.email = m.email
ORDER BY migration_status, e.email;

RAISE NOTICE 'Phase 3 Data Migration Complete - Review migration_verification view';
