-- =====================================================
-- PHASE 1: DATA ANALYSIS & BACKUP
-- Migration: Merge appy_managers into appy_employees
-- =====================================================

-- Create backup tables with timestamp
DO $$
DECLARE
    backup_suffix TEXT := '_backup_' || to_char(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISS');
BEGIN
    -- Backup managers table
    EXECUTE format('CREATE TABLE appy_managers%s AS SELECT * FROM appy_managers', backup_suffix);
    EXECUTE format('CREATE TABLE appy_employees%s AS SELECT * FROM appy_employees', backup_suffix);
    EXECUTE format('CREATE TABLE appy_employee_managers%s AS SELECT * FROM appy_employee_managers', backup_suffix);
    
    RAISE NOTICE 'Backup tables created with suffix: %', backup_suffix;
END $$;

-- Data analysis queries
SELECT 'MANAGERS ANALYSIS' as analysis_type;

SELECT 
    'Total Managers' as metric,
    COUNT(*) as count
FROM appy_managers
UNION ALL
SELECT 
    'Active Managers' as metric,
    COUNT(*) as count
FROM appy_managers WHERE active = true
UNION ALL
SELECT 
    'Managers with Hierarchy' as metric,
    COUNT(*) as count
FROM appy_managers WHERE manager_id IS NOT NULL
UNION ALL
SELECT 
    'Managers with Additional Roles' as metric,
    COUNT(*) as count
FROM appy_managers WHERE jsonb_array_length(additional_roles) > 0;

SELECT 'EMPLOYEES ANALYSIS' as analysis_type;

SELECT 
    'Total Employees' as metric,
    COUNT(*) as count
FROM appy_employees
UNION ALL
SELECT 
    'Active Employees' as metric,
    COUNT(*) as count
FROM appy_employees WHERE active = true
UNION ALL
SELECT 
    'Employees with Clerk ID' as metric,
    COUNT(*) as count
FROM appy_employees WHERE clerk_user_id IS NOT NULL
UNION ALL
SELECT 
    'Employees with Manager ID' as metric,
    COUNT(*) as count
FROM appy_employees WHERE manager_id IS NOT NULL;

SELECT 'OVERLAP ANALYSIS' as analysis_type;

-- Find overlapping users (exist in both tables)
SELECT 
    'Users in Both Tables' as metric,
    COUNT(*) as count
FROM appy_managers m 
INNER JOIN appy_employees e ON m.email = e.email;

-- Find managers without employee records
SELECT 
    'Managers Only (no employee record)' as metric,
    COUNT(*) as count
FROM appy_managers m 
LEFT JOIN appy_employees e ON m.email = e.email 
WHERE e.email IS NULL;

-- Find employees without manager records
SELECT 
    'Employees Only (no manager record)' as metric,
    COUNT(*) as count
FROM appy_employees e 
LEFT JOIN appy_managers m ON e.email = m.email 
WHERE m.email IS NULL;

SELECT 'CONFLICT ANALYSIS' as analysis_type;

-- Check for name conflicts
SELECT 
    'Name Conflicts' as conflict_type,
    COUNT(*) as count
FROM appy_managers m 
INNER JOIN appy_employees e ON m.email = e.email 
WHERE m.full_name != e.full_name;

-- Check for role conflicts
SELECT 
    'Role Rank Conflicts' as conflict_type,
    COUNT(*) as count
FROM appy_managers m 
INNER JOIN appy_employees e ON m.email = e.email 
WHERE (m.role = 'super-admin' AND e.role_rank != 'super-admin')
   OR (m.role = 'senior-manager' AND e.role_rank != 'senior-manager')
   OR (m.role = 'manager' AND e.role_rank NOT IN ('manager', 'senior-manager'));

-- Detailed conflict report
SELECT 'DETAILED CONFLICTS' as analysis_type;

SELECT 
    m.email,
    m.full_name as manager_name,
    e.full_name as employee_name,
    m.role as manager_role,
    e.role_rank as employee_role_rank,
    CASE 
        WHEN m.full_name != e.full_name THEN 'NAME_MISMATCH'
        WHEN m.role != e.role_rank THEN 'ROLE_MISMATCH'
        ELSE 'NO_CONFLICT'
    END as conflict_type
FROM appy_managers m 
INNER JOIN appy_employees e ON m.email = e.email 
WHERE m.full_name != e.full_name 
   OR m.role != e.role_rank
ORDER BY conflict_type, m.email;

-- Foreign key dependency analysis
SELECT 'FOREIGN KEY DEPENDENCIES' as analysis_type;

SELECT 
    'Appraisals referencing managers' as dependency,
    COUNT(DISTINCT manager_id) as unique_manager_ids,
    COUNT(*) as total_references
FROM appy_appraisals;

SELECT 
    'Employee-Manager relationships' as dependency,
    COUNT(DISTINCT manager_id) as unique_manager_ids,
    COUNT(*) as total_relationships
FROM appy_employee_managers;

-- Check for orphaned references
SELECT 'ORPHANED REFERENCES' as analysis_type;

SELECT 
    'Appraisals with invalid manager_id' as orphan_type,
    COUNT(*) as count
FROM appy_appraisals a
LEFT JOIN appy_managers m ON a.manager_id = m.user_id
WHERE m.user_id IS NULL;

SELECT 
    'Employee-Manager relationships with invalid manager_id' as orphan_type,
    COUNT(*) as count
FROM appy_employee_managers em
LEFT JOIN appy_managers m ON em.manager_id = m.user_id
WHERE m.user_id IS NULL;

-- Summary report
SELECT 'MIGRATION SUMMARY' as analysis_type;

WITH migration_stats AS (
    SELECT 
        (SELECT COUNT(*) FROM appy_managers) as total_managers,
        (SELECT COUNT(*) FROM appy_employees) as total_employees,
        (SELECT COUNT(*) FROM appy_managers m INNER JOIN appy_employees e ON m.email = e.email) as overlapping_users,
        (SELECT COUNT(*) FROM appy_managers m LEFT JOIN appy_employees e ON m.email = e.email WHERE e.email IS NULL) as managers_only,
        (SELECT COUNT(*) FROM appy_employees e LEFT JOIN appy_managers m ON e.email = m.email WHERE m.email IS NULL) as employees_only
)
SELECT 
    'Total records to migrate: ' || total_managers as summary,
    'Existing employee records to update: ' || overlapping_users as detail1,
    'New employee records to create: ' || managers_only as detail2,
    'Employee records unchanged: ' || employees_only as detail3
FROM migration_stats;

RAISE NOTICE 'Phase 1 Analysis Complete - Review results before proceeding to Phase 2';
