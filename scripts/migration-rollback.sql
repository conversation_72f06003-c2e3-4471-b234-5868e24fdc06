-- =====================================================
-- MIGRATION ROLLBACK SCRIPT
-- Use this to rollback the migration if needed
-- =====================================================

-- WARNING: This script will undo all migration changes
-- Only run this if you need to rollback the migration

DO $$
DECLARE
    backup_suffix TEXT;
    backup_exists BOOLEAN := false;
BEGIN
    -- Find the most recent backup
    SELECT table_name INTO backup_suffix
    FROM information_schema.tables 
    WHERE table_name LIKE 'appy_managers_backup_%'
    ORDER BY table_name DESC
    LIMIT 1;
    
    IF backup_suffix IS NOT NULL THEN
        backup_exists := true;
        backup_suffix := substring(backup_suffix from 'appy_managers_backup_(.*)');
        RAISE NOTICE 'Found backup with suffix: %', backup_suffix;
    ELSE
        RAISE EXCEPTION 'No backup tables found! Cannot rollback safely.';
    END IF;
    
    IF backup_exists THEN
        RAISE NOTICE 'Starting rollback process...';
        
        -- Step 1: Remove added columns from appy_employees
        RAISE NOTICE 'Removing added columns...';
        ALTER TABLE appy_employees DROP COLUMN IF EXISTS system_role;
        ALTER TABLE appy_employees DROP COLUMN IF EXISTS additional_roles;
        
        -- Step 2: Restore original data from backups
        RAISE NOTICE 'Restoring original employee data...';
        
        -- Clear current employees table and restore from backup
        TRUNCATE appy_employees CASCADE;
        EXECUTE format('INSERT INTO appy_employees SELECT * FROM appy_employees_backup_%s', backup_suffix);
        
        -- Clear current managers table and restore from backup  
        TRUNCATE appy_managers CASCADE;
        EXECUTE format('INSERT INTO appy_managers SELECT * FROM appy_managers_backup_%s', backup_suffix);
        
        -- Clear current employee_managers table and restore from backup
        TRUNCATE appy_employee_managers CASCADE;
        EXECUTE format('INSERT INTO appy_employee_managers SELECT * FROM appy_employee_managers_backup_%s', backup_suffix);
        
        -- Step 3: Drop migration artifacts
        DROP VIEW IF EXISTS migration_verification;
        DROP TABLE IF EXISTS public.migration_log;
        DROP FUNCTION IF EXISTS validate_employee_role_consistency();
        
        -- Step 4: Remove indexes that were added
        DROP INDEX IF EXISTS idx_appy_employees_system_role;
        -- Note: Keep idx_appy_employees_clerk_user_id and idx_appy_employees_email as they might be useful
        
        RAISE NOTICE 'Rollback completed successfully!';
        RAISE NOTICE 'Backup tables preserved for safety: appy_managers_backup_%, appy_employees_backup_%', backup_suffix, backup_suffix;
        
    END IF;
END $$;

-- Verification queries after rollback
SELECT 'ROLLBACK VERIFICATION' as verification_type;

-- Check that columns were removed
SELECT 
    column_name
FROM information_schema.columns 
WHERE table_name = 'appy_employees' 
AND column_name IN ('system_role', 'additional_roles');

-- Check record counts match original
SELECT 
    'appy_managers' as table_name,
    COUNT(*) as current_count
FROM appy_managers
UNION ALL
SELECT 
    'appy_employees' as table_name,
    COUNT(*) as current_count
FROM appy_employees
UNION ALL
SELECT 
    'appy_employee_managers' as table_name,
    COUNT(*) as current_count
FROM appy_employee_managers;

RAISE NOTICE 'Rollback verification complete - check that record counts match pre-migration state';
