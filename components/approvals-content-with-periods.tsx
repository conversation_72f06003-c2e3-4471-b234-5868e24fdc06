"use client"

import { useState } from "react"
import { Check<PERSON><PERSON><PERSON>, Clock, TrendingUp, Calendar } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ApprovalsDashboardWrapper } from "@/components/approvals-dashboard-wrapper"
import { PeriodMonthFilter, usePeriodMonthFilter } from "@/components/period-month-filter"
import { formatPeriodName } from "@/lib/utils/period-helpers"
import type { AppraisalPeriod, PendingApproval } from "@/lib/types"

interface ApprovalStatistics {
  totalPending: number
  totalInProgress: number
  totalCompleted: number
  avgApprovalTime: number
}

interface ApprovalsContentWithPeriodsProps {
  initialPendingApprovals: PendingApproval[]
  initialCompletedApprovals: PendingApproval[]
  initialStatistics: ApprovalStatistics
  periods: AppraisalPeriod[]
}

export function ApprovalsContentWithPeriods({ 
  initialPendingApprovals,
  initialCompletedApprovals,
  initialStatistics,
  periods
}: ApprovalsContentWithPeriodsProps) {
  const [pendingApprovals, setPendingApprovals] = useState(initialPendingApprovals)
  const [completedApprovals, setCompletedApprovals] = useState(initialCompletedApprovals)
  const [statistics, setStatistics] = useState(initialStatistics)
  const [loading, setLoading] = useState(false)
  
  // Use the period month filter hook
  const {
    selectedPeriod,
    selectedMonth,
    selectedYear,
    handlePeriodChange: onPeriodChange
  } = usePeriodMonthFilter()

  // Handle period change with data fetching
  const handlePeriodFilterChange = async (value: string, month: number, year: number) => {
    try {
      setLoading(true)
      onPeriodChange(value, month, year)

      console.log('[APPROVALS] Period changed to:', { month, year, value })

      // Fetch data for the specific month/year period
      const response = await fetch(`/api/approvals?month=${month}&year=${year}`)

      if (!response.ok) {
        throw new Error(`Failed to fetch approvals data: ${response.statusText}`)
      }

      const result = await response.json()

      if (result.success) {
        setPendingApprovals(result.data.pendingApprovals)
        setCompletedApprovals(result.data.completedApprovals)
        setStatistics(result.data.statistics)
        console.log('[APPROVALS] Successfully loaded data for period:', { month, year })
      } else {
        throw new Error(result.error || 'Failed to fetch approvals data')
      }

    } catch (err) {
      console.error('[APPROVALS] Error loading data for period:', err)
      // TODO: Show user-friendly error message
    } finally {
      setLoading(false)
    }
  }

  // Get current period info for display
  const periodDisplayName = formatPeriodName(selectedMonth, selectedYear)
  
  // Create array of existing period values for the filter
  const existingPeriodValues = periods.map(period => {
    const startDate = new Date(period.periodStart)
    const year = startDate.getFullYear()
    const month = startDate.getMonth() + 1
    return `${year}-${String(month).padStart(2, '0')}`
  })

  return (
    <div className="space-y-6">
      {/* Period Filter */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-blue-600" />
            Period Filter
          </CardTitle>
          <CardDescription>
            Filter approvals by period - {periodDisplayName}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <PeriodMonthFilter
            value={selectedPeriod}
            onChange={handlePeriodFilterChange}
            disabled={loading}
            showPeriodStatus={true}
            existingPeriods={existingPeriodValues}
            className="max-w-md"
          />
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{statistics.totalPending}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting your approval
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{statistics.totalInProgress}</div>
            <p className="text-xs text-muted-foreground">
              Multi-level approvals
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{statistics.totalCompleted}</div>
            <p className="text-xs text-muted-foreground">
              Fully approved
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Approval Time</CardTitle>
            <Calendar className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {statistics.avgApprovalTime.toFixed(1)}
            </div>
            <p className="text-xs text-muted-foreground">
              Days to complete
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Approvals Table with Filtering */}
      <ApprovalsDashboardWrapper
        pendingApprovals={pendingApprovals}
        completedApprovals={completedApprovals}
      />
    </div>
  )
}