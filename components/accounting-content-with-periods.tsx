"use client"

import { useState } from "react"
import { Calculator, DollarSign, FileText } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { AccountingDashboardTable } from "@/components/accounting-dashboard-table"
import { AccountingStatsCards, AccountingSummary } from "@/components/accounting-stats-cards"
import { PeriodMonthFilter, usePeriodMonthFilter } from "@/components/period-month-filter"
import { formatPeriodName, parseMonthYear } from "@/lib/utils/period-helpers"
import type { AppraisalPeriod, AccountingStats, UserRole } from "@/lib/types"

interface AccountingContentWithPeriodsProps {
  initialData: any[]
  initialStats: AccountingStats
  periods: AppraisalPeriod[]
  currentUserRole?: UserRole
}

export function AccountingContentWithPeriods({ 
  initialData, 
  initialStats, 
  periods,
  currentUserRole
}: AccountingContentWithPeriodsProps) {
  const [accountingData, setAccountingData] = useState(initialData)
  const [accountingStats, setAccountingStats] = useState(initialStats)
  const [loading, setLoading] = useState(false)
  
  // Use the period month filter hook
  const {
    selectedPeriod,
    selectedMonth,
    selectedYear,
    handlePeriodChange: onPeriodChange
  } = usePeriodMonthFilter()

  // Handle period change with data fetching
  const handlePeriodFilterChange = async (value: string, month: number, year: number) => {
    try {
      setLoading(true)
      onPeriodChange(value, month, year)

      console.log('[ACCOUNTING] Period changed to:', { month, year, value })

      // Fetch data for the specific month/year period
      const response = await fetch(`/api/accounting?month=${month}&year=${year}`)

      if (!response.ok) {
        throw new Error(`Failed to fetch accounting data: ${response.statusText}`)
      }

      const result = await response.json()

      if (result.success) {
        setAccountingData(result.data)
        setAccountingStats(result.stats)
        console.log('[ACCOUNTING] Successfully loaded data for period:', result.period)
      } else {
        throw new Error(result.error || 'Failed to fetch accounting data')
      }

    } catch (err) {
      console.error('[ACCOUNTING] Error loading data for period:', err)
      // TODO: Show user-friendly error message
    } finally {
      setLoading(false)
    }
  }

  // Get current period info for display
  const periodDisplayName = formatPeriodName(selectedMonth, selectedYear)
  
  // Create array of existing period values for the filter
  const existingPeriodValues = periods.map(period => {
    const startDate = new Date(period.periodStart)
    const year = startDate.getFullYear()
    const month = startDate.getMonth() + 1
    return `${year}-${String(month).padStart(2, '0')}`
  })

  return (
    <div className="space-y-6">
      {/* Summary Overview */}
      <AccountingSummary stats={accountingStats} />
      
      {/* Stats Cards */}
      <AccountingStatsCards stats={accountingStats} />
      
      {/* Main Data Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-green-600" />
                Employee Payment Data
              </CardTitle>
              <CardDescription>
                {`${periodDisplayName} - `}
                Manage employee payments and export payroll data
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {/* Enhanced Period Filter */}
              <PeriodMonthFilter
                value={selectedPeriod}
                onChange={handlePeriodFilterChange}
                disabled={loading}
                showPeriodStatus={true}
                showExternalBadges={false}
                existingPeriods={existingPeriodValues}
              />
              <Badge variant="outline" className="bg-green-100 text-green-800">
                <Calculator className="mr-1 h-3 w-3" />
                {accountingData.length} Employee{accountingData.length !== 1 ? 's' : ''}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <AccountingDashboardTable data={accountingData} currentUserRole={currentUserRole} />
        </CardContent>
      </Card>
      
      {/* Help Information */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="text-blue-800 flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Export Information
          </CardTitle>
        </CardHeader>
        <CardContent className="text-blue-700">
          <div className="space-y-2 text-sm">
            <p>• <strong>Ready to Pay:</strong> Employees approved for payment processing</p>
            <p>• <strong>CSV Export:</strong> Only includes employees ready for payment or with submitted appraisals</p>
            <p>• <strong>Hours:</strong> Automatically calculated for hourly employees (160 hours standard)</p>
            <p>• <strong>Payment Status:</strong> Based on appraisal submission and approval status</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

console.log('💰 [ACCOUNTING] Accounting content with periods component loaded')
