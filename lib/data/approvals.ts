import { supabaseAdmin } from '../supabase-admin'
import type { 
  ApprovalWorkflow, 
  ApprovalStep, 
  ApprovalHistory, 
  PendingApproval,
  ApprovalWorkflowStatus,
  ApprovalStepStatus,
  ApprovalStepType,
  ApprovalAction
} from '../types'
import { debug } from '../debug'

/**
 * Create multi-level approval workflow for an appraisal
 */
export async function createApprovalWorkflow(
  appraisalId: string, 
  employeeId: string
): Promise<{ success: boolean; workflowId?: string; error?: string }> {
  try {
    debug.log('🔄 [APPROVALS] Creating approval workflow:', { appraisalId, employeeId })

    const { data, error } = await supabaseAdmin
      .rpc('create_approval_workflow', {
        p_appraisal_id: appraisalId,
        p_employee_id: employeeId
      })

    if (error) {
      console.error('❌ [APPROVALS] Failed to create workflow:', error)
      return { success: false, error: error.message }
    }

    debug.log('✅ [APPROVALS] Workflow created successfully:', data)
    return { success: true, workflowId: data }

  } catch (error) {
    console.error('🚨 [APPROVALS] Error creating workflow:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }
  }
}

/**
 * Get pending approvals for a specific user
 */
export async function getPendingApprovalsForUser(userId: string, userEmail?: string): Promise<PendingApproval[]> {
  try {
    debug.log('📋 [APPROVALS] Fetching pending approvals for user:', { userId, userEmail })

    const { data, error } = await supabaseAdmin
      .rpc('get_pending_approvals_for_user', {
        p_user_id: userId,
        p_user_email: userEmail || null
      })

    if (error) {
      console.error('❌ [APPROVALS] Failed to fetch pending approvals:', error)
      return []
    }

    interface PendingApprovalData {
      workflow_id: string
      appraisal_id: string
      employee_id: string
      employee_name: string
      department_name: string
      step_level: number
      step_id: string
      submission_date: string
      days_pending: number
    }

    return ((data || []) as PendingApprovalData[]).map((item) => ({
      workflowId: item.workflow_id,
      appraisalId: item.appraisal_id,
      employeeId: item.employee_id,
      employeeName: item.employee_name,
      departmentName: item.department_name,
      stepLevel: item.step_level,
      stepId: item.step_id,
      submissionDate: item.submission_date,
      daysPending: item.days_pending
    }))

  } catch (error) {
    console.error('🚨 [APPROVALS] Error fetching pending approvals:', error)
    return []
  }
}

/**
 * Process approval step (approve or reject)
 */
export async function processApprovalStep(
  stepId: string,
  action: 'approved' | 'rejected',
  actorId: string,
  actorName: string,
  comments?: string,
  rejectionReason?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    debug.log('⚡ [APPROVALS] Processing approval step:', {
      stepId,
      action,
      actorId
    })

    const { data, error } = await supabaseAdmin
      .rpc('process_approval_step', {
        p_step_id: stepId,
        p_action: action,
        p_actor_id: actorId,
        p_actor_name: actorName,
        p_comments: comments || null,
        p_rejection_reason: rejectionReason || null
      })

    if (error) {
      console.error('❌ [APPROVALS] Failed to process approval step:', error)
      return { success: false, error: error.message }
    }

    debug.log('✅ [APPROVALS] Approval step processed successfully')
    return { success: true }

  } catch (error) {
    console.error('🚨 [APPROVALS] Error processing approval step:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }
  }
}

/**
 * Get approval workflow details by ID
 */
export async function getApprovalWorkflow(workflowId: string): Promise<ApprovalWorkflow | null> {
  try {
    debug.log('🔍 [APPROVALS] Fetching workflow details:', workflowId)

    const { data, error } = await supabaseAdmin
      .from('appy_approval_workflows')
      .select('*')
      .eq('id', workflowId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      console.error('❌ [APPROVALS] Failed to fetch workflow:', error)
      return null
    }

    if (!data) {
      return null
    }

    return {
      id: data.id,
      appraisalId: data.appraisal_id,
      workflowType: data.workflow_type,
      currentLevel: data.current_level,
      totalLevels: data.total_levels,
      status: data.status,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      completedAt: data.completed_at
    }

  } catch (error) {
    console.error('🚨 [APPROVALS] Error fetching workflow:', error)
    return null
  }
}

/**
 * Get approval steps for a workflow
 */
export async function getApprovalSteps(workflowId: string): Promise<ApprovalStep[]> {
  try {
    debug.log('📋 [APPROVALS] Fetching approval steps for workflow:', workflowId)

    const { data, error } = await supabaseAdmin
      .from('appy_approval_steps')
      .select(`
        *,
        approver:approver_id(full_name)
      `)
      .eq('workflow_id', workflowId)
      .order('step_level', { ascending: true })

    if (error) {
      console.error('❌ [APPROVALS] Failed to fetch approval steps:', error)
      return []
    }

    interface ApprovalStepData {
      id: string
      workflow_id: string
      step_level: number
      approver_id: string | null
      approver_role: string | null
      approver?: {
        full_name: string
      } | null
      step_type: string
      status: ApprovalStepStatus
      approved_at: string | null
      rejected_at: string | null
      rejection_reason: string | null
      comments: string | null
      created_at: string
    }

    return ((data || []) as ApprovalStepData[])
      .filter(step => step.approver_id !== null)
      .map((step) => ({
        id: step.id,
        workflowId: step.workflow_id,
        stepLevel: step.step_level,
        approverId: step.approver_id!,
      approverRole: step.approver_role,
      approverName: step.approver?.full_name || null,
      stepType: step.step_type as ApprovalStepType,
      status: step.status,
      approvedAt: step.approved_at,
      rejectedAt: step.rejected_at,
      rejectionReason: step.rejection_reason,
      comments: step.comments,
      createdAt: step.created_at
    }))

  } catch (error) {
    console.error('🚨 [APPROVALS] Error fetching approval steps:', error)
    return []
  }
}

/**
 * Get approval history for a workflow
 */
export async function getApprovalHistory(workflowId: string): Promise<ApprovalHistory[]> {
  try {
    debug.log('📜 [APPROVALS] Fetching approval history for workflow:', workflowId)

    const { data, error } = await supabaseAdmin
      .from('appy_approval_history')
      .select('*')
      .eq('workflow_id', workflowId)
      .order('created_at', { ascending: true })

    if (error) {
      console.error('❌ [APPROVALS] Failed to fetch approval history:', error)
      return []
    }

    interface ApprovalHistoryData {
      id: string
      workflow_id: string
      step_id: string | null
      action: ApprovalAction
      actor_id: string
      actor_name: string
      previous_status: ApprovalWorkflowStatus | null
      new_status: ApprovalWorkflowStatus
      comments: string | null
      metadata: Record<string, unknown> | null
      created_at: string
    }

    return ((data || []) as ApprovalHistoryData[]).map((item) => ({
      id: item.id,
      workflowId: item.workflow_id,
      stepId: item.step_id,
      action: item.action,
      actorId: item.actor_id,
      actorName: item.actor_name,
      previousStatus: item.previous_status,
      newStatus: item.new_status,
      comments: item.comments,
      metadata: item.metadata,
      createdAt: item.created_at
    }))

  } catch (error) {
    console.error('🚨 [APPROVALS] Error fetching approval history:', error)
    return []
  }
}

/**
 * Get workflow by appraisal ID
 */
export async function getWorkflowByAppraisalId(appraisalId: string): Promise<ApprovalWorkflow | null> {
  try {
    debug.log('🔍 [APPROVALS] Fetching workflow for appraisal:', appraisalId)

    const { data, error } = await supabaseAdmin
      .from('appy_approval_workflows')
      .select('*')
      .eq('appraisal_id', appraisalId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      console.error('❌ [APPROVALS] Failed to fetch workflow by appraisal:', error)
      return null
    }

    if (!data) {
      return null
    }

    return {
      id: data.id,
      appraisalId: data.appraisal_id,
      workflowType: data.workflow_type,
      currentLevel: data.current_level,
      totalLevels: data.total_levels,
      status: data.status,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      completedAt: data.completed_at
    }

  } catch (error) {
    console.error('🚨 [APPROVALS] Error fetching workflow by appraisal:', error)
    return null
  }
}

/**
 * Check if user can approve a specific step
 */
export async function canUserApproveStep(stepId: string, userId: string): Promise<boolean> {
  try {
    // First check user role
    const { data: managerData } = await supabaseAdmin
      .from('appy_managers')
      .select('role')
      .eq('user_id', userId)
      .eq('active', true)
      .single()

    const userRole = managerData?.role
    const isSuperAdmin = userRole === 'super-admin'
    const isSeniorManager = userRole === 'senior-manager'

    // Get step data
    const { data, error } = await supabaseAdmin
      .from('appy_approval_steps')
      .select(`
        *,
        workflow:workflow_id(current_level, status)
      `)
      .eq('id', stepId)
      .single()

    if (error || !data) {
      return false
    }

    // Check for null workflow data
    if (!data.workflow) {
      return false
    }

    // Step must be in pending status and workflow must be active
    if (data.status !== 'pending' || data.workflow.status !== 'in_progress') {
      return false
    }

    // Super-admin can approve any pending step
    if (isSuperAdmin) {
      return true
    }

    // Senior-managers can approve any step at their level or below (Level 1 and Level 2)
    if (isSeniorManager && (data.step_level === 1 || data.step_level === 2)) {
      // For Level 1 steps: can approve if workflow is at Level 1 OR if they're assigned
      // For Level 2 steps: can approve if they're assigned (regardless of current_level)
      if (data.step_level === 1) {
        return data.workflow.current_level === 1 || data.approver_id === userId
      } else if (data.step_level === 2) {
        return data.approver_id === userId
      }
    }

    // Regular approval logic: must be assigned approver and step must be at current workflow level
    return (
      data.approver_id === userId &&
      data.step_level === data.workflow.current_level
    )

  } catch (error) {
    console.error('🚨 [APPROVALS] Error checking approval permission:', error)
    return false
  }
}

/**
 * Get approval statistics for dashboard
 */
export async function getApprovalStatistics(userId?: string): Promise<{
  totalPending: number
  totalInProgress: number
  totalCompleted: number
  avgApprovalTime: number
}> {
  try {
    debug.log('📊 [APPROVALS] Fetching approval statistics')

    let query = supabaseAdmin
      .from('appy_approval_workflows')
      .select('status, created_at, completed_at')

    if (userId) {
      // Filter by workflows where user is an approver
      const { data: approverWorkflows } = await supabaseAdmin
        .from('appy_approval_steps')
        .select('workflow_id')
        .eq('approver_id', userId)

      const workflowIds = approverWorkflows?.map(w => w.workflow_id) || []
      if (workflowIds.length > 0) {
        query = query.in('id', workflowIds)
      } else {
        // If user is not an approver for any workflows, return empty stats
        return {
          totalPending: 0,
          totalInProgress: 0,
          totalCompleted: 0,
          avgApprovalTime: 0
        }
      }
    }

    const { data, error } = await query

    if (error) {
      console.error('❌ [APPROVALS] Failed to fetch statistics:', error)
      return { totalPending: 0, totalInProgress: 0, totalCompleted: 0, avgApprovalTime: 0 }
    }

    const stats = {
      totalPending: 0,
      totalInProgress: 0,
      totalCompleted: 0,
      avgApprovalTime: 0
    }

    let totalApprovalTime = 0
    let completedCount = 0

    interface WorkflowStatData {
      status: ApprovalWorkflowStatus
      created_at: string
      completed_at: string | null
    }

    (data as WorkflowStatData[] | null)?.forEach((workflow) => {
      switch (workflow.status) {
        case 'pending':
          stats.totalPending++
          break
        case 'in_progress':
          stats.totalInProgress++
          break
        case 'completed':
          stats.totalCompleted++
          if (workflow.completed_at && workflow.created_at) {
            const approvalTime = new Date(workflow.completed_at).getTime() - new Date(workflow.created_at).getTime()
            totalApprovalTime += approvalTime / (1000 * 60 * 60 * 24) // Convert to days
            completedCount++
          }
          break
      }
    })

    if (completedCount > 0) {
      stats.avgApprovalTime = totalApprovalTime / completedCount
    }

    return stats

  } catch (error) {
    console.error('🚨 [APPROVALS] Error fetching statistics:', error)
    return { totalPending: 0, totalInProgress: 0, totalCompleted: 0, avgApprovalTime: 0 }
  }
}

/**
 * Get completed approvals for a specific user
 */
export async function getCompletedApprovalsForUser(userId: string, userEmail?: string): Promise<PendingApproval[]> {
  try {
    debug.log('📋 [APPROVALS] Fetching completed approvals for user:', { userId, userEmail })

    const { data, error } = await supabaseAdmin
      .rpc('get_completed_approvals_for_user', {
        p_user_id: userId,
        p_user_email: userEmail || null
      })

    if (error) {
      console.error('❌ [APPROVALS] Failed to fetch completed approvals:', error)
      return []
    }

    interface CompletedApprovalData {
      workflow_id: string
      appraisal_id: string
      employee_id: string
      employee_name: string
      department_name: string
      step_level: number
      step_id: string
      submission_date: string
      completed_date: string
      status: 'approved' | 'rejected'
      days_to_complete: number
    }

    return ((data || []) as CompletedApprovalData[]).map((item) => ({
      workflowId: item.workflow_id,
      appraisalId: item.appraisal_id,
      employeeId: item.employee_id,
      employeeName: item.employee_name,
      departmentName: item.department_name,
      stepLevel: item.step_level,
      stepId: item.step_id,
      submissionDate: item.submission_date,
      completedDate: item.completed_date,
      status: item.status,
      daysPending: item.days_to_complete // Reuse this field for days to complete
    }))

  } catch (error) {
    console.error('🚨 [APPROVALS] Error fetching completed approvals:', error)
    return []
  }
}

/**
 * Get pending approvals for a specific user with optional period filtering
 */
export async function getPendingApprovalsForUserWithPeriod(
  userId: string, 
  userEmail?: string,
  month?: number,
  year?: number
): Promise<PendingApproval[]> {
  try {
    debug.log('📋 [APPROVALS] Fetching pending approvals with period filter:', { userId, userEmail, month, year })

    const { data, error } = await supabaseAdmin
      .rpc('get_pending_approvals_for_user_with_period', {
        p_user_id: userId,
        p_user_email: userEmail || null,
        p_month: month || null,
        p_year: year || null
      })

    if (error) {
      console.error('❌ [APPROVALS] Failed to fetch pending approvals with period:', error)
      // Fallback to original function if the new RPC doesn't exist yet
      return getPendingApprovalsForUser(userId, userEmail)
    }

    interface PendingApprovalData {
      workflow_id: string
      appraisal_id: string
      employee_id: string
      employee_name: string
      department_name: string
      step_level: number
      step_id: string
      submission_date: string
      days_pending: number
    }

    return ((data || []) as PendingApprovalData[]).map((item) => ({
      workflowId: item.workflow_id,
      appraisalId: item.appraisal_id,
      employeeId: item.employee_id,
      employeeName: item.employee_name,
      departmentName: item.department_name,
      stepLevel: item.step_level,
      stepId: item.step_id,
      submissionDate: item.submission_date,
      daysPending: item.days_pending
    }))

  } catch (error) {
    console.error('🚨 [APPROVALS] Error fetching pending approvals with period:', error)
    // Fallback to original function
    return getPendingApprovalsForUser(userId, userEmail)
  }
}

/**
 * Get completed approvals for a specific user with optional period filtering
 */
export async function getCompletedApprovalsForUserWithPeriod(
  userId: string, 
  userEmail?: string,
  month?: number,
  year?: number
): Promise<PendingApproval[]> {
  try {
    debug.log('📋 [APPROVALS] Fetching completed approvals with period filter:', { userId, userEmail, month, year })

    const { data, error } = await supabaseAdmin
      .rpc('get_completed_approvals_for_user_with_period', {
        p_user_id: userId,
        p_user_email: userEmail || null,
        p_month: month || null,
        p_year: year || null
      })

    if (error) {
      console.error('❌ [APPROVALS] Failed to fetch completed approvals with period:', error)
      // Fallback to original function if the new RPC doesn't exist yet
      return getCompletedApprovalsForUser(userId, userEmail)
    }

    interface CompletedApprovalData {
      workflow_id: string
      appraisal_id: string
      employee_id: string
      employee_name: string
      department_name: string
      step_level: number
      step_id: string
      submission_date: string
      completed_date: string
      status: 'approved' | 'rejected'
      days_to_complete: number
    }

    return ((data || []) as CompletedApprovalData[]).map((item) => ({
      workflowId: item.workflow_id,
      appraisalId: item.appraisal_id,
      employeeId: item.employee_id,
      employeeName: item.employee_name,
      departmentName: item.department_name,
      stepLevel: item.step_level,
      stepId: item.step_id,
      submissionDate: item.submission_date,
      completedDate: item.completed_date,
      status: item.status,
      daysPending: item.days_to_complete
    }))

  } catch (error) {
    console.error('🚨 [APPROVALS] Error fetching completed approvals with period:', error)
    // Fallback to original function
    return getCompletedApprovalsForUser(userId, userEmail)
  }
}

debug.log('🔄 Multi-level approval service initialized')
