import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { getAccountingDataForUserByPeriod, getAccountingStatsByPeriod } from '@/lib/data/accounting'

/**
 * GET /api/accounting - Fetch accounting data with optional period filtering
 * Query parameters:
 * - month: number (1-12)
 * - year: number (e.g., 2024)
 */
export async function GET(request: NextRequest) {
  try {
    console.log('📊 [ACCOUNTING API] Fetching accounting data...')
    
    // Check authentication
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // Check permissions - only accountants, hr-admin, and super-admin can access
    if (!['accountant', 'hr-admin', 'super-admin'].includes(currentUser.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const monthParam = searchParams.get('month')
    const yearParam = searchParams.get('year')

    let month: number | undefined
    let year: number | undefined

    // Parse and validate month/year parameters
    if (monthParam && yearParam) {
      month = parseInt(monthParam, 10)
      year = parseInt(yearParam, 10)

      if (isNaN(month) || month < 1 || month > 12) {
        return NextResponse.json({ error: 'Invalid month parameter (must be 1-12)' }, { status: 400 })
      }

      if (isNaN(year) || year < 2020 || year > 2030) {
        return NextResponse.json({ error: 'Invalid year parameter' }, { status: 400 })
      }

      console.log(`📅 [ACCOUNTING API] Filtering by period: ${month}/${year}`)
    } else {
      console.log('📅 [ACCOUNTING API] No period filter - using current period')
    }

    // Fetch accounting data and stats
    const [accountingData, accountingStats] = await Promise.all([
      getAccountingDataForUserByPeriod(month, year),
      getAccountingStatsByPeriod(month, year)
    ])

    console.log(`✅ [ACCOUNTING API] Successfully fetched ${accountingData.length} records`)

    return NextResponse.json({
      success: true,
      data: accountingData,
      stats: accountingStats,
      period: month && year ? { month, year } : null
    })

  } catch (error) {
    console.error('❌ [ACCOUNTING API] Error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch accounting data' },
      { status: 500 }
    )
  }
}
