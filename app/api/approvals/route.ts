import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { 
  getPendingApprovalsForUser, 
  getCompletedApprovalsForUser,
  getPendingApprovalsForUserWithPeriod,
  getCompletedApprovalsForUserWithPeriod,
  getApprovalStatistics 
} from '@/lib/data/approvals'

/**
 * GET - Get approvals for current user with optional period filtering
 */
export async function GET(request: NextRequest) {
  try {
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has approval permissions
    if (!['manager', 'senior-manager', 'hr-admin', 'admin', 'super-admin'].includes(currentUser.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Parse query parameters for period filtering
    const url = new URL(request.url)
    const month = url.searchParams.get('month')
    const year = url.searchParams.get('year')

    let pendingApprovals, completedApprovals

    // Use period-filtered functions if month and year are provided
    if (month && year) {
      const monthNum = parseInt(month, 10)
      const yearNum = parseInt(year, 10)
      
      if (isNaN(monthNum) || isNaN(yearNum) || monthNum < 1 || monthNum > 12) {
        return NextResponse.json({ 
          error: 'Invalid month or year parameter' 
        }, { status: 400 })
      }

      [pendingApprovals, completedApprovals] = await Promise.all([
        getPendingApprovalsForUserWithPeriod(currentUser.id, currentUser.email, monthNum, yearNum),
        getCompletedApprovalsForUserWithPeriod(currentUser.id, currentUser.email, monthNum, yearNum)
      ])
    } else {
      // Use original functions for all approvals
      [pendingApprovals, completedApprovals] = await Promise.all([
        getPendingApprovalsForUser(currentUser.id, currentUser.email),
        getCompletedApprovalsForUser(currentUser.id, currentUser.email)
      ])
    }

    // Get statistics (always for all periods for now)
    const statistics = await getApprovalStatistics(currentUser.id)

    return NextResponse.json({ 
      success: true, 
      data: {
        pendingApprovals,
        completedApprovals,
        statistics
      }
    })

  } catch (error) {
    console.error('Error fetching approvals:', error)
    return NextResponse.json({ 
      error: 'Failed to fetch approvals' 
    }, { status: 500 })
  }
}